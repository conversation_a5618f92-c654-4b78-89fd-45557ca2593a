#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 简单进度条测试 - 验证修复效果
"""

import tkinter as tk
from tkinter import ttk
import time

def test_progress():
    """测试进度条基本功能"""
    root = tk.Tk()
    root.title("🧪 进度条修复验证")
    root.geometry("500x300")
    
    # 创建进度条
    label = ttk.Label(root, text="测试进度条是否能正常显示进度", font=('Arial', 12))
    label.pack(pady=20)
    
    progress = ttk.Progressbar(root, mode='determinate', maximum=100, length=400)
    progress.pack(pady=20)
    
    percent_label = ttk.Label(root, text="0%", font=('Arial', 14, 'bold'))
    percent_label.pack(pady=10)
    
    def update_progress():
        for i in range(0, 101, 5):
            progress.config(value=i)
            percent_label.config(text=f"{i}%")
            root.update_idletasks()
            root.update()
            time.sleep(0.1)
        
        result_label = ttk.Label(root, text="✅ 测试完成！进度条功能正常", 
                                font=('Arial', 12, 'bold'), foreground='green')
        result_label.pack(pady=20)
    
    start_button = ttk.Button(root, text="🚀 开始测试", command=update_progress)
    start_button.pack(pady=20)
    
    root.mainloop()

if __name__ == "__main__":
    test_progress()
